"""
重构验证测试脚本
"""
import sys
import traceback

def test_imports():
    """测试所有模块的导入"""
    print("开始测试模块导入...")
    
    try:
        # 测试基础模块
        print("1. 测试基础模块...")
        from backend.models import database, schemas
        print("   ✓ 数据库模型导入成功")
        
        # 测试服务模块
        print("2. 测试服务模块...")
        from backend.services import auth_service, ai_service, dataset_service
        from backend.services import file_service, audit_service, index_service
        print("   ✓ 服务模块导入成功")
        
        # 测试工具模块
        print("3. 测试工具模块...")
        from backend.utils import validation, image_utils, file_utils
        print("   ✓ 工具模块导入成功")
        
        # 测试API模块
        print("4. 测试API模块...")
        from backend.api import auth, datasets, files, pages, router
        print("   ✓ API模块导入成功")
        
        # 测试主路由
        print("5. 测试主路由...")
        from urls import router, sync_indexs
        print("   ✓ 主路由导入成功")
        
        print("\n✅ 所有模块导入测试通过！")
        return True
        
    except Exception as e:
        print(f"\n❌ 模块导入失败: {str(e)}")
        traceback.print_exc()
        return False

def test_service_instances():
    """测试服务实例"""
    print("\n开始测试服务实例...")
    
    try:
        from backend.services.auth_service import auth_service
        from backend.services.ai_service import ai_service
        from backend.services.dataset_service import dataset_service
        from backend.services.file_service import file_service
        from backend.services.audit_service import audit_service
        from backend.services.index_service import index_service
        
        # 检查服务实例是否正确创建
        assert auth_service is not None, "认证服务实例为空"
        assert ai_service is not None, "AI服务实例为空"
        assert dataset_service is not None, "数据集服务实例为空"
        assert file_service is not None, "文件服务实例为空"
        assert audit_service is not None, "审核服务实例为空"
        assert index_service is not None, "索引服务实例为空"
        
        print("✅ 所有服务实例测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ 服务实例测试失败: {str(e)}")
        traceback.print_exc()
        return False

def test_api_routes():
    """测试API路由"""
    print("\n开始测试API路由...")
    
    try:
        from backend.api.router import main_router
        
        # 检查路由是否包含预期的路径
        routes = [route.path for route in main_router.routes]
        
        expected_routes = [
            "/",
            "/home/",
            "/index/",
            "/docs/",
            "/login/",
            "/getDatasList/",
            "/uploadfiles/",
            "/getFileInfo/{filename}"
        ]
        
        missing_routes = []
        for expected_route in expected_routes:
            if not any(expected_route in route for route in routes):
                missing_routes.append(expected_route)
        
        if missing_routes:
            print(f"❌ 缺少路由: {missing_routes}")
            return False
        
        print("✅ API路由测试通过！")
        return True
        
    except Exception as e:
        print(f"❌ API路由测试失败: {str(e)}")
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("重构验证测试")
    print("=" * 50)
    
    tests = [
        test_imports,
        test_service_instances,
        test_api_routes
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        if test():
            passed += 1
    
    print("\n" + "=" * 50)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 重构验证成功！所有测试通过。")
        return True
    else:
        print("⚠️  重构验证部分失败，请检查上述错误。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
