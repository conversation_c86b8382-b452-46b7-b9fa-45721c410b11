"""
AI服务模块
"""
import json
from zhipuai import ZhipuAI
from fastapi import HTTPException
from config import zhipukey, system_prompt, bs_prompt
from backend.models.schemas import FileInfo, APIResponse


class AIService:
    """AI服务类"""
    
    def __init__(self):
        self.client = ZhipuAI(api_key=zhipukey)
    
    def get_indexes(self, content: str, prompt: str) -> tuple:
        """使用AI生成索引"""
        try:
            completion = self.client.chat.completions.create(
                model="glm-4-air",
                messages=[
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": content}
                ],
                temperature=0.3,
                response_format={"type": "json_object"}
            )
            chinese_blocks = json.loads(completion.choices[0].message.content)
            gjzsd = chinese_blocks.get("text", "")
            gjc = chinese_blocks.get("text2", "")
            return gjzsd, gjc
        except Exception as e:
            print(f"AI生成索引失败: {e}")
            return "", ""
    
    def extract_product_model(self, filename: str) -> str:
        """从文件名提取产品型号"""
        try:
            prompt = """
                - Role: 信息提取与处理专家
                - Background: 用户需要从文件名称中提取产品型号，并以JSON字符串的形式返回。这通常是为了快速整理和分类文件，提高工作效率。
                - Profile: 你是一位精通文本处理和信息提取的专家，擅长从复杂的文本中快速提取关键信息，并能够准确地将其格式化为所需的结构。
                - Skills: 你具备强大的文本解析能力，能够识别和提取特定模式的信息，同时精通JSON格式的生成和处理。
                - Goals: 从文件名称中准确提取产品型号，并以JSON字符串的形式返回。
                - Constrains: 提取的产品型号必须准确无误，且返回的JSON格式必须符合规范。
                - OutputFormat: JSON字符串，例如 {"产品型号":"xface600"}
                - Workflow:
                1. 接收文件名称作为输入。
                2. 分析文件名称，提取其中的产品型号。
                3. 将提取的产品型号格式化为JSON字符串并返回。
                - Examples:
                - 例子1：文件名称 "xface600使用说明书"
                    输出：{"产品型号":"xface600"}
                - 例子2：文件名称 "cm500功能参数介绍及使用说明"
                    输出：{"产品型号":"cm500"}
                - 例子3：文件名称 "pro3000用户手册"
                    输出：{"产品型号":"pro3000"}
                - Initialization: 在第一次对话中，请直接输出以下：您好！我将帮助您从文件名称中提取产品型号并以JSON格式返回。请提供需要处理的文件名称。
            """
            
            completion = self.client.chat.completions.create(
                model="glm-4-air",
                messages=[
                    {"role": "system", "content": prompt},
                    {"role": "user", "content": filename}
                ],
                temperature=0.3,
                response_format={"type": "json_object"}
            )
            
            chinese_blocks = json.loads(completion.choices[0].message.content)
            return chinese_blocks.get("产品型号", "")
        except Exception as e:
            print(f"提取产品型号失败: {e}")
            return ""
    
    async def get_file_info(self, filename: str) -> APIResponse:
        """从文件名中提取产品信息"""
        try:
            # 检查文件名是否为空
            if not filename or len(filename.strip()) == 0:
                raise ValueError("文件名不能为空")

            # 使用智谱AI提取文件信息
            prompt = """
                - Role: 信息提取与处理专家
                - Background: 用户需要从文件名称中提取产品信息，包括型号、名称、售前/售后类型、可接入软件等，并以JSON字符串的形式返回。
                - Profile: 你是一位精通文本处理和信息提取的专家，擅长从复杂的文本中快速提取关键信息，并能够准确地将其格式化为所需的结构。
                - Skills: 你具备强大的文本解析能力，能够识别和提取特定模式的信息，同时精通JSON格式的生成和处理。
                - Goals: 从文件名称中准确提取产品信息，并以JSON字符串的形式返回。
                - Constrains: 提取的信息必须准确无误，且返回的JSON格式必须符合规范。
                - OutputFormat: JSON字符串，例如 {"型号":"xface600", "名称":"门禁机", "售前售后":"售前", "可接入软件":"万傲瑞达V6600"}
                - Workflow:
                1. 接收文件名称作为输入。
                2. 分析文件名称，提取其中的产品信息。
                3. 将提取的信息格式化为JSON字符串并返回。
                - Examples:
                - 例子1：文件名称 "xface600使用说明书"
                    输出：{"型号":"xface600", "名称":"门禁机", "售前售后":"售前", "可接入软件":"万傲瑞达V6600"}
                - 例子2：文件名称 "cm500功能参数介绍及使用说明"
                    输出：{"型号":"cm500", "名称":"考勤机", "售前售后":"售前", "可接入软件":"百傲瑞达3.0"}
                - 例子3：文件名称 "pro3000用户手册"
                    输出：{"型号":"pro3000", "名称":"门禁机", "售前售后":"售后", "可接入软件":"万傲瑞达V6600"}
                - 例子4：文件名称 "ZKTime5.0考勤软件操作手册"
                    输出：{"型号":"ZKTime5.0", "名称":"考勤软件", "售前售后":"售前", "可接入软件":"ZKTime5.0"}
                - 例子5：文件名称 "ZKBioSecurity3.0安装部署指南"
                    输出：{"型号":"ZKBioSecurity3.0", "名称":"安全管理平台", "售前售后":"售前", "可接入软件":"ZKBioSecurity3.0"}
                - 例子6：文件名称 "万傲瑞达V6600门禁模块使用说明"
                    输出：{"型号":"V6600", "名称":"门禁模块", "售前售后":"售前", "可接入软件":"万傲瑞达V6600"}
                - 例子7：文件名称 "熵基互联平台介绍"
                    输出：{"型号":"熵基互联", "名称":"互联平台", "售前售后":"售前", "可接入软件":"熵基互联"}
                - Initialization: 在第一次对话中，请直接输出以下：您好！我将帮助您从文件名称中提取产品信息并以JSON格式返回。请提供需要处理的文件名称。
            """

            # 调用智谱AI接口
            try:
                completion = self.client.chat.completions.create(
                    model="glm-4-air",
                    messages=[
                        {"role": "system", "content": prompt},
                        {"role": "user", "content": filename}
                    ],
                    temperature=0.3,
                    response_format={"type": "json_object"},
                    timeout=30  # 30秒超时
                )

                response_text = completion.choices[0].message.content

                # 尝试解析JSON响应
                try:
                    file_info = json.loads(response_text)

                    # 验证返回的字段是否完整
                    required_fields = ["型号", "名称", "售前售后", "可接入软件"]
                    for field in required_fields:
                        if field not in file_info:
                            file_info[field] = "未知"

                    return APIResponse(code=200, data=file_info)

                except json.JSONDecodeError as e:
                    print(f"JSON解析失败: {str(e)}, 原始响应: {response_text}")
                    raise ValueError(f"AI返回的结果不是有效的JSON格式: {str(e)}")

            except Exception as e:
                print(f"智谱AI调用失败: {str(e)}")
                raise ValueError(f"AI接口调用失败: {str(e)}")

        except Exception as e:
            print(f"文件信息提取失败: {str(e)}")
            # 返回默认值
            default_info = FileInfo()
            return APIResponse(code=200, data=default_info.dict())


# 全局AI服务实例
ai_service = AIService()
