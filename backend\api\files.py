"""
文件相关API路由
"""
from fastapi import APIRouter, File, Form, UploadFile, Path
from backend.services.file_service import file_service
from backend.services.ai_service import ai_service

router = APIRouter()


@router.post("/uploadfiles/")
async def upload_files(
    file: UploadFile = File(...), 
    type: str = Form(...), 
    data: str = Form(...)
):
    """文件上传"""
    return await file_service.upload_files(file, type, data)


@router.get("/getFileInfo/{filename}")
async def get_file_info(filename: str = Path(...)):
    """获取文件信息"""
    return await ai_service.get_file_info(filename)


@router.post("/savebs/")
async def save_bs(body: dict):
    """保存标书文件"""
    return await file_service.save_bs(body)
