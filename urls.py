# urls.py
from fastapi import APIRouter
from zhishiku_h5 import *

router = APIRouter()
# 定义 URL 路径与方法的映射  zhishiku_h5.py
# 首页路由
router.add_api_route("/", home, methods=["GET"])
router.add_api_route("/home/", home, methods=["GET"])
router.add_api_route("/getHomeDatasetsInfo/", getHomeDatasetsInfo, methods=["GET"])

# 文档库页面路由
router.add_api_route("/index/", index, methods=["GET"])
router.add_api_route("/docs/", index, methods=["GET"])

# 其他API路由
router.add_api_route("/login/", login, methods=["POST"])
router.add_api_route("/deleteQA/", deleteQA, methods=["POST"])
router.add_api_route("/getDatasList/", getDatasList, methods=["GET"])
router.add_api_route("/uploadfiles/", uploadfiles, methods=["POST"])
router.add_api_route("/getCollectionListInfo/", getCollectionListInfo, methods=["GET"])
router.add_api_route("/auditCollection/", auditCollection, methods=["POST"])
router.add_api_route("/getDatasetdatas/", getDatasetdatas, methods=["GET"])
router.add_api_route("/deleteCollection/", deleteCollection, methods=["GET"])
router.add_api_route("/getSameNameFiles/", getSameNameFiles, methods=["GET"])
router.add_api_route("/updateDatasetdatas/", updateDatasetdatas, methods=["POST"])
router.add_api_route("/getFileInfo/{filename}", getFileInfo, methods=["GET"])

# 定义 标书解读 接口地址
router.add_api_route("/savebs/", savebs, methods=["POST"])